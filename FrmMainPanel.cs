using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Net.Sockets;
using System.Net;
using System.ServiceProcess;
using System.Threading;
using System.Windows.Forms;
using CCPanel.shell;
using CCPanel.utils;
using Microsoft.Win32;
using System.Linq;
using TcpServer;
using System.Threading.Tasks;
using Serilog;
using System.Configuration;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Reflection;
using System.Timers;
using System.Management;

namespace CCPanel
{
    // 1.定义委托
    public delegate void DeleReadStdOutput(string result);

    public delegate void DeleReadErrOutput(string result);

    public delegate void ControlAction(Control control, bool enabled);

    public partial class FrmMainPanel : Form
    {
        private static string currentPathWithoutAppName = System.Environment.CurrentDirectory;
        private static Dictionary<String, Dictionary<String, Button>> buttonMap = new Dictionary<String, Dictionary<String, Button>>();
        private static Dictionary<String, Button> mysqlButtonMap = new Dictionary<String, Button>();
        private static Dictionary<String, Button> nginxButtonMap = new Dictionary<String, Button>();
        private static Dictionary<String, Button> redisButtonMap = new Dictionary<String, Button>();
        private static Dictionary<String, Button> emqxButtonMap = new Dictionary<String, Button>();

        private static Process CmdProcess = null;

        private static System.Timers.Timer restartTimer;

        private static TcpListener tcpListener;
        private static Thread ClientListenerThread;

        private static MqttClientTool _mqttClient;
        private static TcpClient client = null;
        private static NetworkStream clientStream = null;

        // 添加客户端连接管理#a
        private readonly List<TcpClient> activeClients = new List<TcpClient>();
        private readonly List<Thread> clientThreads = new List<Thread>();
        private readonly object clientLock = new object();
        // 添加客户端连接管理#a


        string configFileName = currentPathWithoutAppName + "/CPanelConfig.ini";
        private static string iniGroupNameTEL = "TEL";
        private static string iniGroupNameMQ = "MQ";
        private static string iniGroupNameSF = "SF";

        private static string CONFIG_KEY_IP = "ip";
        private static string CONFIG_KEY_PORT = "port";
        private static string CONFIG_KEY_SAVEIP = "remember";
        private static string CONFIG_KEY_AUTO_RUN_SERVER = "autoRunServer";
        private static string CONFIG_KEY_ALARM_REPORT = "alarmReport";
        private static string CONFIG_KEY_ALARM_SYSTEMCODE = "systemCode";

        private static string CONFIG_MQTT_IP = "mqtt_ip";
        private static string CONFIG_MQTT_PORT = "mqtt_port";
        private static string CONFIG_MQTT_TYPE = "type";


        // 2.定义委托事件
        public event DeleReadStdOutput ReadStdOutput;
        public event DeleReadErrOutput ReadErrOutput;

        #region 初始化面板按钮的启用禁用

        public FrmMainPanel()
        {
            InitializeComponent();
            this.linkLabelOpenNginxWeb.Visible = false;

            this.btnUrlConfig.Enabled = true;
            this.btnMysqlStatus.Enabled = false;
            this.btnEmqxStatus.Enabled = false;
            this.btnRedisStatus.Enabled = false;
            this.btnNginxStatus.Enabled = false;

            this.btnEmqxServiceCreate.Enabled = false;
            this.btnNginxServiceCreate.Enabled = false;
            this.btnRedisServiceCreate.Enabled = false;
            this.btnMysqlServiceCreate.Enabled = false;

            redisButtonMap.Add("start", btnRedisStart);
            redisButtonMap.Add("stop", btnRedisStop);
            redisButtonMap.Add("status", btnRedisStatus);
            redisButtonMap.Add("create", btnRedisServiceCreate);

            mysqlButtonMap.Add("start", btnMysqlStart);
            mysqlButtonMap.Add("stop", btnMysqlStop);
            mysqlButtonMap.Add("status", btnMysqlStatus);
            mysqlButtonMap.Add("create", btnMysqlServiceCreate);

            nginxButtonMap.Add("start", btnNginxStart);
            nginxButtonMap.Add("stop", btnNginxStop);
            nginxButtonMap.Add("status", btnNginxStatus);
            nginxButtonMap.Add("create", btnNginxServiceCreate);

            emqxButtonMap.Add("start", btnEmqxStart);
            emqxButtonMap.Add("stop", btnEmqxStop);
            emqxButtonMap.Add("status", btnEmqxStatus);
            emqxButtonMap.Add("create", btnEmqxServiceCreate);

            buttonMap.Add("redis", redisButtonMap);
            buttonMap.Add("mysql", mysqlButtonMap);
            buttonMap.Add("nginx", nginxButtonMap);
            buttonMap.Add("emqx", emqxButtonMap);


            CMDInit();
            IPAddress currentIp = GetLocalIpAddress();
            AppendLog($"IP地址:{currentIp}", this.txtLogInfo);
            ConnectToMqttServer();
        }

        #endregion

        #region 主窗体加载前初始化

        private void formMainPanel_Load(object sender, EventArgs e)
        {
            this.linklabelPathShow.Text = currentPathWithoutAppName;
            this.timerServiceChecker.Start();
            this.timerTCPClientCount.Start();
            this.timerEnvCheck.Start();

            this.timerLogClear.Interval = 30 * 60 * 1000;
            this.timerLogClear.Start();

            //this.nfForm.Visible = true;//托盘显示
            bool f = PreEnvCheck();
            if (f)
            {
                this.btnPreEnvCheck.Enabled = false;
                this.btnPreEnvCheck.Text = "VC++2013已安装";
                this.tabControlMain.SelectTab(tpageCmdRun);
            }
            else
            {
                this.tabControlMain.SelectTab(tpageInit);
                this.btnPreEnvCheck.Enabled = true;
            }
        }

        #endregion

        #region 定时器检测,提供给timer使用的服务监测

        private void timerServiceChecker_Tick(object sender, EventArgs e)
        {
            ServiceCheck();
        }

        private void ServiceCheck()
        {
            foreach (String serviceName in GConfig.serviceNameArray)
            {
                buttonMap.TryGetValue(serviceName, out Dictionary<String, Button> btnMap);
                btnMap.TryGetValue("start", out Button btnStart);
                btnMap.TryGetValue("stop", out Button btnStop);
                btnMap.TryGetValue("status", out Button btnStatus);
                btnMap.TryGetValue("create", out Button btnCreate);


                if (SCUtils.ServiceCheckExist(serviceName))
                {
                    btnCreate.Enabled = false;
                    btnStatus.Enabled = false;
                    try
                    {
                        ServiceController sc = new ServiceController(serviceName, ".");
                        sc.Refresh();
                        if (sc.Status == System.ServiceProcess.ServiceControllerStatus.Stopped)
                        {
                            btnStart.Enabled = true;
                            btnStop.Enabled = false;
                            btnStatus.Text = GConfig.MSG_SERVICE_STOP;
                        }
                        else if (sc.Status == System.ServiceProcess.ServiceControllerStatus.Running)
                        {
                            btnStart.Enabled = false;
                            btnStop.Enabled = true;
                            btnStatus.Text = GConfig.MSG_SERVICE_START;
                        }

                        sc.Close();
                        sc.Dispose();
                    }
                    catch (Exception e)
                    {
                        MessageBox.Show(e.Message);
                    }
                }
                else
                {
                    btnStatus.Text = GConfig.MSG_SERVICE_NOT_EXIST;
                    btnStart.Enabled = false;
                    btnStop.Enabled = false;
                    btnCreate.Enabled = true;
                }
            }
        }

        #endregion

        #region 打开日志目录

        private void btnLogExplore_Click(object sender, EventArgs e)
        {
            String sfLogPath = IniFileUtils.ReadValue(configFileName, FrmUrlConfigPage.CONFIG_URL_CONFIG, FrmUrlConfigPage.CONFIG_URL_SF_SFSYSTEM_LOG);
            bool flag = Directory.Exists(sfLogPath);
            if (!flag)
            {
                sfLogPath = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            }

            System.Diagnostics.Process.Start(sfLogPath);
        }

        #endregion

        #region 打开面板目录

        private void linklabelPathShow_LinkDoubleClicked(object sender, MouseEventArgs e)
        {
            System.Diagnostics.Process.Start(this.linklabelPathShow.Text);
        }

        #endregion

        #region 室分系统启动专用cmd

        private void CMDInit()
        {
            //3.将相应函数注册到委托事件中
            ReadStdOutput += new DeleReadStdOutput(ReadStdOutputAction);
            ReadErrOutput += new DeleReadErrOutput(ReadErrOutputAction);
        }

        private void Excute(string WorkingDirectory, string StartFileName, string StartFileArg)
        {
            Boolean flag = checkJavaProcess();
            if (flag)
            {
                MessageBox.Show("程序已经启动,若要重新启动,请先关闭后再启动!");
                return;
            }

            CmdProcess = new Process();
            CmdProcess.StartInfo.WorkingDirectory = WorkingDirectory; //工作目录
            CmdProcess.StartInfo.FileName = WorkingDirectory + StartFileName; // 命令
            CmdProcess.StartInfo.Arguments = StartFileArg; // 参数
            CmdProcess.StartInfo.CreateNoWindow = true; // 不创建新窗口
            CmdProcess.StartInfo.UseShellExecute = false;
            CmdProcess.StartInfo.RedirectStandardInput = true; // 重定向输入
            CmdProcess.StartInfo.RedirectStandardOutput = true; // 重定向标准输出
            CmdProcess.StartInfo.RedirectStandardError = true; // 重定向错误输出
            //CmdProcess.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
            CmdProcess.OutputDataReceived += new DataReceivedEventHandler((object sender, DataReceivedEventArgs e) =>
                {
                    if (e.Data != null)
                    {
                        // 4. 异步调用，需要invoke  托管启动的服务的进程与本进程解耦，服务进程输入控制台信息的管道有输入能及时输出，不会造成启动服务进程的阻塞
                        this.BeginInvoke(ReadStdOutput, new object[] { e.Data });
                    }
                }
            );
            CmdProcess.ErrorDataReceived += new DataReceivedEventHandler((object sender, DataReceivedEventArgs e) =>
                {
                    if (e.Data != null)
                    {
                        this.Invoke(ReadErrOutput, new object[] { e.Data });
                    }
                }
            );

            CmdProcess.EnableRaisingEvents = true; // 启用Exited事件
            CmdProcess.Exited += new EventHandler(CmdProcess_Exited); // 注册进程结束事件
            CmdProcess.Start();
            CmdProcess.BeginOutputReadLine();
            CmdProcess.BeginErrorReadLine();
        }

        private void ReadStdOutputAction(string result)
        {
            this.rboxResult.AppendText(result + "\r\n");
        }

        private void ReadErrOutputAction(string result)
        {
            //this.tabControlMain.SelectTab(tpageError);
            rtextboxErrorResult.AppendText(result + "\r\n");
        }

        private void CmdProcess_Exited(object sender, EventArgs e)
        {
            // 执行结束后触发
        }

        #endregion

        #region 室分程序执行

        private void BtnCmdRun_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确认启动?", "室分网管系统启动确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }

            string serverDirPath = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            // Excute(serverDirPath, GConfig.INDOOR_SYSTEM_SERVER_NAME, argStr);  // 加密程序启动
            Excute(currentPathWithoutAppName + GConfig.PATH_HOME_JAVA_ENV + "/bin/", "java.exe", " -jar " + serverDirPath + "sftx-server.jar"); //jar包直接启动
        }

        //关闭室分程序
        private void btnServerStop_Click(object sender, EventArgs e)
        {
            closeJAVAprocess();
        }

        private Boolean checkJavaProcess()
        {
            int javaPId = GetJavaProcessId("java -jar sftx-server.jar");
            if (javaPId != -1)
            {
                return true;
            }

            return false;
        }

        private void closeJAVAprocess()
        {
            int javaPId = GetJavaProcessId("java -jar sftx-server.jar");
            Process javap = null;
            if (javaPId != -1)
            {
                javap = Process.GetProcessById(javaPId);
            }

            if (javap != null)
            {
                DialogResult dr = MessageBox.Show($"当前程序[{javaPId}]正在运行中,确认要关闭程序?", "关闭确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (dr == DialogResult.Yes)
                {
                    if (dr == DialogResult.Yes)
                    {
                        killProcess(javap);
                        this.rboxResult.Clear();
                    }
                }
            }
        }

        #endregion

        #region 关闭程序前确认

        private void FrmMainPanel_FormClosing(object sender, FormClosingEventArgs e)
        {
            DialogResult isClose = MessageBox.Show("是否关闭当前程序?", "关闭提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (isClose != DialogResult.Yes)
            {
                e.Cancel = true;
                return;
            }

            // 按照要求的顺序关闭服务：先关闭TCP服务，然后关闭MQTT
            CloseServicesInOrder();
            closeJAVAprocess();
        }

        /// <summary>
        /// 按顺序关闭服务：先关闭TCP服务，然后关闭MQTT
        /// </summary>
        private async void CloseServicesInOrder()
        {
            try
            {
                // 1. 先关闭TCP服务
                if (tcpListener != null)
                {
                    Log.Information("[关闭]开始关闭TCP服务...");
                    AppendLog("[关闭]开始关闭TCP服务...", txtLogInfo);

                    // 调用现有的TCP关闭逻辑
                    btnTcpClose_Click(null, null);

                    Log.Information("[关闭]TCP服务已关闭");
                    AppendLog("[关闭]TCP服务已关闭", txtLogInfo);
                }

                // 2. 然后关闭MQTT连接
                if (_mqttClient != null && _mqttClient.IsConnected)
                {
                    Log.Information("[关闭]开始关闭MQTT连接...");
                    AppendLog("[关闭]开始关闭MQTT连接...", txtLogInfo);

                    await _mqttClient.DisconnectAsync();

                    Log.Information("[关闭]MQTT连接已关闭");
                    AppendLog("[关闭]MQTT连接已关闭", txtLogInfo);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[关闭]关闭服务时发生错误: {ex.Message}");
                AppendLog($"[关闭]关闭服务时发生错误: {ex.Message}", txtLogInfo);
            }
        }

        #endregion

        #region 日志清空

        private void btnErrorResultCls_Click(object sender, EventArgs e)
        {
            this.rtextboxErrorResult.Clear();
        }

        private void btnResultCls_Click(object sender, EventArgs e)
        {
            this.rboxResult.Clear();
        }

        #endregion

        #region Mysql服务

        //Mysql 服务创建
        [STAThread]
        private void btnMysqlServiceCreate_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_MYSQL;
            bool serviceExist = SCUtils.ServiceCheckExist(serviceName);
            if (serviceExist)
            {
                return;
            }

            System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_MYSQL;
            string fileName = GConfig.FILE_NAME_MYSQL;
            if (principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator))
            {
                Process.Start(dir + fileName);
            }
            else
            {
                System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                startInfo.UseShellExecute = true;
                startInfo.WorkingDirectory = dir;
                startInfo.FileName = fileName;
                startInfo.Verb = "runas";
                System.Diagnostics.Process.Start(startInfo);
            }
        }

        private void btnMysqlStart_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_MYSQL;
            SCUtils.ServiceStart(serviceName);
        }

        private void btnMysqlStop_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_MYSQL;
            SCUtils.ServiceStop(serviceName);
        }

        #endregion

        #region Nginx服务

        /**
        * nginx服务创建
        */
        private void btnNginxServiceCreate_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_NGINX;
            string argument = "install";
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX;
            string fileName = GConfig.FILE_NAME_NGINX;

            SCUtils.ExcuteByCShell(serviceName, dir, fileName, argument);
        }

        //Nginx服务启动
        private void btnNginxStart_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_NGINX;
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX;
            string fileName = GConfig.FILE_NAME_NGINX;
            string argument = "start";
            SCUtils.ExcuteByCShell(serviceName, dir, fileName, argument);
        }

        //Nginx服务关闭
        private void btnNginxStop_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_NGINX;
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX;
            string fileName = GConfig.FILE_NAME_NGINX;
            string argument = "stop";
            SCUtils.ExcuteByCShell(serviceName, dir, fileName, argument);
        }

        //Nginx进程强行杀死
        private void btnNginxFClose_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("是否强制关闭Nginx进程?", "Nginx强制关闭", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                try
                {
                    SCUtils.ServiceStop(GConfig.SERVICE_NAME_NGINX);
                    ProcessUtils.KillProcess(GConfig.SERVICE_NAME_NGINX);
                    MessageBox.Show("已强制关闭", "Nginx强制关闭", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    ProcessUtils.KillProcess(GConfig.SERVICE_NAME_NGINX);
                    MessageBox.Show("已强制关闭成功," + ex.Message, "Nginx强制关闭", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
            }
            else
            {
                return;
            }
        }

        #endregion

        #region MQTT服务

        //MQTT创建服务
        private void btnEmqxServiceCreate_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_EMQX;
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_EMQX;
            string fileName = GConfig.FILE_NAME_EMQX;
            string argument = "install";
            SCUtils.ExcuteByCShell(serviceName, dir, fileName, argument);
        }

        //MQTT启动服务
        private void btnEmqxStart_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_EMQX;
            SCUtils.ServiceStart(serviceName);
        }

        private void btnEmqxStop_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_EMQX;
            SCUtils.ServiceStop(serviceName);
        }

        #endregion

        #region Redis服务

        /**
         * redis服务创建
         */
        private void btnRedisServiceCreate_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_REDIS;
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_REDIS;
            string fileName = GConfig.FILE_NAME_REDIS;
            string argument = "--service-install redis.windows.conf --loglevel verbose";
            SCUtils.ExcuteByCShell(serviceName, dir, fileName, argument);
        }

        private void btnRedisStart_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_REDIS;
            SCUtils.ServiceStart(serviceName);
        }

        private void btnRedisStop_Click(object sender, EventArgs e)
        {
            string serviceName = GConfig.SERVICE_NAME_REDIS;
            SCUtils.ServiceStop(serviceName);
        }

        #endregion

        #region 打开文件目录事件集

        private void btnDirSfServerHome_Click(object sender, EventArgs e)
        {
            //室分系统
            string dir = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            openDir(dir);
        }

        private void btnDirMysql_Click(object sender, EventArgs e)
        {
            //mysql home
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_MYSQL;
            openDir(dir);
        }

        private void btnDirNginxHome_Click(object sender, EventArgs e)
        {
            //nginx home
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX;
            openDir(dir);
        }

        private void btnDirNginxConfig_Click(object sender, EventArgs e)
        {
            //nginx 配置文件
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX_CFG;
            openDir(dir);
        }

        private void btnDirNginxHtml_Click(object sender, EventArgs e)
        {
            //前端程序存放目录
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_NGINX_WEB;
            openDir(dir);
        }


        private void btnDirEmqx_Click(object sender, EventArgs e)
        {
            //EMQX目录
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_EMQX;
            openDir(dir);
        }

        private void btnDirJava_Click(object sender, EventArgs e)
        {
            //java目录
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_JAVA;
            openDir(dir);
        }

        /**
         * 路径配置
         * */
        private void btnMysqlTools_Click(object sender, EventArgs e)
        {
            new FrmUrlConfigPage().ShowDialog();
        }


        private void btnMysqlTool2_Click(object sender, EventArgs e)
        {
            // Mysql客户端工具打开
            string dir = currentPathWithoutAppName + GConfig.PATH_HOME_MYSQL_TOOLS2;
            string toolFilePath = dir + GConfig.FILE_NAME_MYSQL_TOOLS2;
            if (File.Exists(toolFilePath))
            {
                System.Diagnostics.Process.Start(toolFilePath);
            }
            else
            {
                MessageBox.Show("目标文件不存在");
            }
        }

        private void openDir(string dir)
        {
            bool flag = Directory.Exists(dir);
            if (!flag)
            {
                MessageBox.Show("目录不存在");
                return;
            }

            try
            {
                System.Diagnostics.Process.Start(dir);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        #endregion

        #region java环境变量一键设置

        private void btnEnvSet_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("该操作将对系统环境变量进行设置,是否[一键设置]JAVA?", "警告", MessageBoxButtons.YesNo);
            if (result == DialogResult.No)
            {
                return;
            }

            string javaHomePath = currentPathWithoutAppName + GConfig.PATH_HOME_JAVA_ENV;
            if (Directory.Exists(javaHomePath))
            {
                string javaHome = "JAVA_HOME";
                Environment.SetEnvironmentVariable(javaHome, javaHomePath, EnvironmentVariableTarget.Machine);
                MessageBox.Show("1==>JAVA_HOME设置完毕");

                string classPath = "CLASSPATH";
                string classPathValue = ".;" + javaHomePath + "/lib/dt.jar;" + javaHomePath + "/lib/tools.jar;";
                Environment.SetEnvironmentVariable(classPath, classPathValue, EnvironmentVariableTarget.Machine);
                MessageBox.Show("2==>PATH设置完毕");

                string path = "PATH";
                string pathValue = Environment.GetEnvironmentVariable(path, EnvironmentVariableTarget.Machine);
                string k1 = currentPathWithoutAppName + GConfig.PATH_HOME_JAVA + "bin";
                string k2 = currentPathWithoutAppName + GConfig.PATH_HOME_JAVA + "jre/bin";
                bool k1Exsit = false;
                bool k2Exsit = false;

                string[] paths = pathValue.Split(';');
                foreach (string p in paths)
                {
                    if (p.ToLower().Trim().Equals(k1.ToLower().Trim()))
                    {
                        k1Exsit = true;
                        continue;
                    }

                    if (p.ToLower().Trim().Equals(k2.ToLower().Trim()))
                    {
                        k2Exsit = true;
                        continue;
                    }
                }

                if (k1Exsit && k2Exsit)
                {
                    MessageBox.Show("3==>PATH环境变量存在JAVA,跳过该步骤!");
                }

                if (!k1Exsit && !k2Exsit)
                {
                    pathValue = k1 + ";" + k2 + ";" + pathValue;
                }
                else if (k1Exsit && !k2Exsit)
                {
                    pathValue = k2 + ";" + pathValue;
                }
                else if (k2Exsit && !k1Exsit)
                {
                    pathValue = k1 + ";" + pathValue;
                }

                Environment.SetEnvironmentVariable(path, pathValue, EnvironmentVariableTarget.Machine);
                MessageBox.Show("3==>PATH设置完毕");
                MessageBox.Show("END==>环境变量设置完毕!");
                initPathList();
            }
            else
            {
                MessageBox.Show("JDK目录不存在", "目录不存在", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
        }

        #endregion

        #region 主窗体托盘化处理

        private void FrmMainPanel_SizeChanged(object sender, EventArgs e)
        {
            //if (this.WindowState == FormWindowState.Minimized)
            //{
            //    this.WindowState = FormWindowState.Minimized;
            //    this.TopMost = false;
            //    this.Visible = false;
            //}
            //else if (this.WindowState == FormWindowState.Normal)
            //{
            //    this.Visible = true;
            //    this.WindowState = FormWindowState.Normal;
            //    this.TopMost = true;
            //    this.TopMost = false;
            //}
        }

        private void nfForm_DoubleClick(object sender, EventArgs e)
        {
            //this.Visible = true;
            //this.WindowState = FormWindowState.Normal;
            //this.TopMost = true;
            //this.TopMost = false;
        }

        #endregion

        #region 环境初始化

        //VC++环境检测
        private bool PreEnvCheck()
        {
            bool exist = true;
            using (RegistryKey regLocalMachine = Registry.LocalMachine)
            {
                try
                {
                    RegistryKey regist = regLocalMachine.OpenSubKey("SOFTWARE", true)
                        .OpenSubKey("Wow6432Node", true)
                        .OpenSubKey("Microsoft", true)
                        .OpenSubKey("VisualStudio", true)
                        .OpenSubKey("12.0", true)
                        .OpenSubKey("VC", true)
                        .OpenSubKey("Runtimes", true)
                        .OpenSubKey("x64", true);
                    if (regist.GetValue("Installed") == null || !regist.GetValue("Installed").ToString().Equals("1"))
                    {
                        exist = false;
                    }
                    else
                    {
                        exist = true;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                }
            }

            return exist;
        }

        //数据库初始化
        private void btnMysqlInit_Click(object sender, EventArgs e)
        {
            backworkerDataDelete.RunWorkerAsync();
        }

        // VC++2013环境安装按钮
        private void btnPreEnvCheck_Click(object sender, EventArgs e)
        {
            bool envExsit = PreEnvCheck();
            if (!envExsit)
            {
                DialogResult rs = MessageBox.Show("当前系统未安装VC++2013运行环境,是否安装?", "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (rs == DialogResult.Yes)
                {
                    string file = currentPathWithoutAppName + GConfig.PATH_CONFIG_RUNTIME + GConfig.FILE_NAME_RUNTIME_VC2013_X64;
                    if (File.Exists(file))
                    {
                        System.Diagnostics.Process.Start(file);
                    }
                    else
                    {
                        MessageBox.Show("目标文件不存在");
                    }
                }
            }
        }

        // NGINX redis emqx 一键初始化
        private void btnInitRedis_Click(object sender, EventArgs e)
        {
            DialogResult rs = MessageBox.Show("确认?:您确认要初始化软件服务??", "建议第一次安装时使用", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
            if (rs == DialogResult.OK)
            {
                string dir = currentPathWithoutAppName + GConfig.PATH_HOME_SOFTWARE;
                string fileName = dir + GConfig.FILE_NAME_SOFT_INIT;

                if (!File.Exists(fileName))
                {
                    MessageBox.Show(GConfig.FILE_NAME_SOFT_INIT + "文件不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
                if (principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator))
                {
                    Process.Start(fileName);
                }
                else
                {
                    System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                    startInfo.UseShellExecute = true;
                    startInfo.WorkingDirectory = dir;
                    startInfo.FileName = fileName;
                    startInfo.Verb = "runas";
                    System.Diagnostics.Process.Start(startInfo);
                }
            }
        }

        private void backworkerDataDelete_DoWork(object sender, DoWorkEventArgs e)
        {
            DialogResult rs = MessageBox.Show("即将执行[初始化数据库操作],[希望您真的明白目前的操作]!", "警告:只建议[!!重装Mysql]时使用该功能!", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning);
            if (rs == DialogResult.Yes)
            {
                DialogResult rs2 = MessageBox.Show("再次确认:您要执行初始化数据库操作????!!!", "警告:再次建议[!!重装Mysql]时使用该功能!", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (rs2 == DialogResult.OK)
                {
                    string dir = currentPathWithoutAppName + GConfig.PATH_HOME_MYSQL;
                    string fileName = GConfig.File_NAME_MYSQL_UNINSTALL;
                    if (Directory.Exists(dir))
                    {
                        Process p = null;
                        System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                        System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
                        this.Enabled = false;
                        if (principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator))
                        {
                            p = Process.Start(dir + fileName);
                        }
                        else
                        {
                            System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                            startInfo.UseShellExecute = true;
                            startInfo.WorkingDirectory = dir;
                            startInfo.FileName = fileName;
                            startInfo.Verb = "runas";
                            p = System.Diagnostics.Process.Start(startInfo);
                        }

                        p.EnableRaisingEvents = true;
                        p.Exited += new EventHandler(mysqlInitProcessIsExist);
                    }
                }
            }
        }

        private void mysqlInitProcessIsExist(object sender, EventArgs e)
        {
            try
            {
                string dir = currentPathWithoutAppName + GConfig.PATH_HOME_MYSQL;
                string dataDir = dir + "data";
                if (Directory.Exists(dataDir))
                {
                    Directory.Delete(dir + "data", true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("删除data文件夹时发生错误,请重新执行!", "请重新执行", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            MessageBox.Show("Mysql初始化完毕", "初始化", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Enabled = true;
        }

        //面板启动前的前置环境检查,是否安装VC++环境变量
        private void timerEnvCheck_Tick(object sender, EventArgs e)
        {
            bool f = PreEnvCheck();
            if (f)
            {
                this.btnPreEnvCheck.Enabled = false;
            }
            else
            {
                this.btnPreEnvCheck.Enabled = true;
            }
        }

        #endregion

        #region 托盘菜单事件

        private void cmsDisplay_Click(object sender, EventArgs e)
        {
            this.Visible = true;
            this.WindowState = FormWindowState.Normal;
            this.TopMost = true;
            this.TopMost = false;
        }

        private void cmsAppExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        #endregion

        #region 获取系统path环境

        private void btnGetPathList_Click(object sender, EventArgs e)
        {
            initPathList();
        }

        /**
         * 初始化path列表
         */
        private void initPathList()
        {
            this.listBoxPathList.Items.Clear();
            string path = "PATH";
            string pathValue = Environment.GetEnvironmentVariable(path, EnvironmentVariableTarget.Machine);
            string[] pathList = pathValue.Split(';');
            for (int i = 0; i < pathList.Length; i++)
            {
                this.listBoxPathList.Items.Add(pathList[i]);
            }
        }

        //选中path
        private void listBoxPathList_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.lbPathValue.Text = this.listBoxPathList.Text.Trim();
        }

        //删除path
        private void lbPathDelete_Click(object sender, EventArgs e)
        {
            string path = "PATH";
            string deletePath = this.lbPathValue.Text;
            if (string.IsNullOrEmpty(deletePath.Trim()))
            {
                return;
            }

            DialogResult rs = MessageBox.Show("确认要删除该" + deletePath + "地址么？", "删除前确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
            if (rs == DialogResult.OK)
            {
                listBoxPathList.Items.Remove(deletePath);
                int itemCount = listBoxPathList.Items.Count;
                string newPathList = "";
                for (int i = 0; i < itemCount; i++)
                {
                    string itemValue = listBoxPathList.Items[i].ToString();
                    if (!string.IsNullOrEmpty(itemValue.Trim()))
                    {
                        newPathList += (listBoxPathList.Items[i].ToString() + ";");
                    }
                }

                Environment.SetEnvironmentVariable(path, newPathList, EnvironmentVariableTarget.Machine);
                initPathList();
            }
        }

        #endregion

        #region 打开浏览器

        private static void OpenDefaultBrowserUrl(string url)
        {
            try
            {
                RegistryKey key = Registry.ClassesRoot.OpenSubKey(@"http\shell\open\command\");
                if (key != null)
                {
                    string s = key.GetValue("").ToString();
                    var lastIndex = s.IndexOf(".exe", StringComparison.Ordinal);
                    if (lastIndex == -1)
                    {
                        lastIndex = s.IndexOf(".EXE", StringComparison.Ordinal);
                    }

                    var path = s.Substring(1, lastIndex + 3);
                    var result = Process.Start(path, url);
                    if (result == null)
                    {
                        var result1 = Process.Start("explorer.exe", url);
                        if (result1 == null)
                        {
                            Process.Start(url);
                        }
                    }
                }
                else
                {
                    var result1 = Process.Start("explorer.exe", url);
                    if (result1 == null)
                    {
                        Process.Start(url);
                    }
                }
            }
            catch
            {
                MessageBox.Show("无法打开,请手动使用浏览器进行访问!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void linkLabelOpenEmqxWeb_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            string url = "http://127.0.0.1:18083";
            OpenDefaultBrowserUrl(url);
        }

        private void linkLabelOpenNginxWeb_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            string url = "http://127.0.0.1:9216";
            OpenDefaultBrowserUrl(url);
        }

        #endregion

        private void btnTcpStart_Click(object sender, EventArgs e)
        {
            string systemCode = this.txtSystemCode.Text;
            if (systemCode == null || systemCode.Equals(""))
            {
                AppendLog("SystemCode不能为空", txtLogInfo);
                return;
            }

            // 检查MQTT连接状态
            if (_mqttClient == null || !_mqttClient.IsConnected)
            {
                AppendLog("错误：MQTT服务未连接，无法启动TCP服务！请先确保MQTT连接成功。", txtLogInfo);
                Log.Warning("尝试启动TCP服务失败：MQTT服务未连接");
                return;
            }

            TcpServerStart();
        }

        private void btnTcpClose_Click(object sender, EventArgs e)
        {
            try
            {
                // 关闭所有活跃的客户端连接
                lock (clientLock)
                {
                    foreach (var client in activeClients)
                    {
                        try
                        {
                            client?.Close();
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"关闭客户端连接时发生错误: {ex.Message}");
                        }
                    }

                    activeClients.Clear();

                    // 等待所有客户端线程结束
                    foreach (var thread in clientThreads)
                    {
                        try
                        {
                            if (thread.IsAlive)
                            {
                                thread.Join(1000); // 等待1秒
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"等待客户端线程结束时发生错误: {ex.Message}");
                        }
                    }

                    clientThreads.Clear();
                }

                // 关闭客户端监听线程
                if (ClientListenerThread != null && ClientListenerThread.IsAlive)
                {
                    try
                    {
                        ClientListenerThread.Join(1000);
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"关闭客户端监听线程时发生错误: {ex.Message}");
                    }
                }

                ClientListenerThread = null;

                // 停止TCP监听
                if (tcpListener != null)
                {
                    try
                    {
                        tcpListener.Stop();
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"停止TCP监听时发生错误: {ex.Message}");
                    }

                    tcpListener = null;
                    Log.Information($"TCP监听已关闭!");
                    AppendLog("关闭TCP服务", txtLogInfo);
                }

                DisableButton(btnTcpStart, true);
                AppendLog("已主动断开所有连接", txtLogInfo);
            }
            catch (Exception ex)
            {
                AppendLog($"关闭TCP服务时发生错误: {ex.Message}", txtLogInfo);
                Log.Error($"关闭TCP服务时发生错误: {ex.Message}");
            }
        }

        private void loadSystemConfig()
        {
            if (File.Exists(configFileName))
            {
                string ip = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_IP);
                Regex regex = new Regex(@"^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$");

                string port = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_PORT);
                string remember = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_SAVEIP);
                string tcpAutoRun = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_AUTO_RUN_SERVER);
                string alarmReport = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_ALARM_REPORT);
                String systemCode = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_ALARM_SYSTEMCODE);

                if (regex.IsMatch(ip))
                {
                    this.txtIpAddress.Text = ip;
                }

                this.txtSystemCode.Text = systemCode;
                if (systemCode == null || systemCode.Equals(""))
                {
                    AppendLog("SystemCode不存在,请填入分配的SystemCode并保存配置!!!!", txtLogInfo);
                }

                //port
                int _port = 0;
                if (int.TryParse(port, out _port))
                {
                    this.txtPort.Text = port;
                }

                //记忆
                if (remember != null)
                {
                    bool flag = false;
                    bool.TryParse(remember, out flag);
                    cboxSaveIp.Checked = flag;
                }

                //TCP自启动
                if (tcpAutoRun != null)
                {
                    bool flag = false;
                    bool.TryParse(tcpAutoRun, out flag);
                    cboxTELAutoRun.Checked = flag;
                }

                //主动上报
                if (alarmReport != null)
                {
                    bool flag = false;
                    bool.TryParse(alarmReport, out flag);
                    cboxAlarmReportFlag.Checked = flag;
                }
            }
            else
            {
                AppendLog("配置文件不存在,将采用默认配置!", txtLogInfo);
            }
        }

        /**
         * TCP服务启动前的前置检查
         */
        private void TcpServerStartPreCheck()
        {
            string systemCode = this.txtSystemCode.Text;
            if (systemCode == null || systemCode.Trim().Equals(""))
            {
                AppendLog("[Panel]请填入分配的SystemCode[10进制]后，手动启动服务!", txtLogInfo);
                return;
            }

            // 检查MQTT连接状态
            if (_mqttClient == null || !_mqttClient.IsConnected)
            {
                AppendLog("[Panel]MQTT服务未连接，跳过TCP服务自动启动。请先确保MQTT连接成功后再手动启动TCP服务。", txtLogInfo);
                Log.Warning("[Panel]TCP服务自动启动失败：MQTT服务未连接");
                return;
            }

            string tcpAutoRun = IniFileUtils.ReadValue(configFileName, iniGroupNameTEL, CONFIG_KEY_AUTO_RUN_SERVER);
            bool flag = false;
            bool.TryParse(tcpAutoRun, out flag);
            if (flag)
            {
                TcpServerStart();
                Log.Information("[Panel]TEL服务启动完毕");
                AppendLog("[Panel]TEL服务启动完毕", txtLogInfo);
            }
            else
            {
                Log.Information("[Panel]当前配置为手动启动模式,请手动启动TEL服务!");
                AppendLog("[Panel]当前配置为手动启动模式,请手动启动TEL服务!", txtLogInfo);
            }
        }

        private void TcpServerStart()
        {
            // 先检查是否已经有一个TCP服务在运行
            if (tcpListener != null)
            {
                AppendLog("TCP服务已在运行中，请先停止当前服务再启动新服务", txtLogInfo);
                return;
            }

            string systemCode = this.txtSystemCode.Text;
            string systemCodeHex = int.Parse(systemCode).ToString("X");
            string ipAddress = txtIpAddress.Text;
            int port;
            if (int.TryParse(txtPort.Text, out port))
            {
                try
                {
                    tcpListener = new TcpListener(IPAddress.Parse(ipAddress), port);
                    tcpListener.Start();

                    ClientListenerThread = new Thread(ListenForClients);
                    ClientListenerThread.IsBackground = true;
                    ClientListenerThread.Start();

                    Log.Information($"室分网管集中告警服务已启动,{ipAddress}:{port},SystemCode编号10进制:{systemCode}|16进制:{systemCodeHex} ", txtLogInfo);
                    AppendLog($"室分网管集中告警服务已启动,{ipAddress}:{port},SystemCode编号10进制:{systemCode}|16进制:{systemCodeHex} ", txtLogInfo);
                }
                catch (Exception ex)
                {
                    // 发生异常时清理已创建的资源
                    if (tcpListener != null)
                    {
                        tcpListener.Stop();
                        tcpListener = null;
                    }

                    if (ClientListenerThread != null)
                    {
                        if (ClientListenerThread.IsAlive)
                        {
                            ClientListenerThread.Abort();
                        }

                        ClientListenerThread = null;
                    }

                    Log.Error($"启动 TCP 服务器失败。错误信息：{ex.Message}");
                    AppendLog($"启动 TCP 服务器失败。错误信息：{ex.Message}", txtLogInfo);
                }
            }
            else
            {
                Log.Error("端口号必须是数字");
                AppendLog("端口号必须是数字", txtLogInfo);
            }
        }

        private void ListenForClients()
        {
            try
            {
                while (tcpListener != null) // 添加检查确保tcpListener未被释放
                {
                    IAsyncResult ar = tcpListener.BeginAcceptTcpClient(AcceptCallback, tcpListener);
                    ar.AsyncWaitHandle.WaitOne();
                }
            }
            catch (ObjectDisposedException)
            {
                // 这是正常情况，当tcpListener被关闭时会抛出此异常
                Log.Information("TCP监听已正常关闭");
                AppendLog("TCP监听已正常关闭", txtLogInfo);
            }
            catch (Exception ex)
            {
                if (tcpListener != null) // 只在非正常关闭时记录错误
                {
                    Log.Error($"TCP监听过程异常:{ex.Message.ToString()}");
                    AppendLog(ex.Message.ToString(), txtLogInfo);
                }
            }
        }

        private void AcceptCallback(IAsyncResult ar)
        {
            TcpListener listener = (TcpListener)ar.AsyncState;
            try
            {
                if (listener == null)
                {
                    return;
                }

                TcpClient newClient = listener.EndAcceptTcpClient(ar);
                Log.Information($"连接成功:客户端连接信息:{newClient.Client.RemoteEndPoint}");
                AppendLog($"连接成功:客户端连接信息:{newClient.Client.RemoteEndPoint}", txtLogInfo);
                DisableButton(btnTcpStart, false);

                // 管理客户端连接和线程
                lock (clientLock)
                {
                    activeClients.Add(newClient);
                    Thread clientThread = new Thread(() => HandleClientComm(newClient));
                    clientThread.IsBackground = true;
                    clientThreads.Add(clientThread);
                    clientThread.Start();
                }
            }
            catch (ObjectDisposedException)
            {
                Log.Information("TCP监听器已关闭，停止接受新连接");
            }
            catch (Exception ex)
            {
                if (tcpListener != null)
                {
                    Log.Error($"连接时出现异常:{ex}");
                    AppendLog($"连接时出现异常:{ex.Message}", txtLogInfo);
                }
            }
        }

        #region HandleClientComm=>集中告警报文接收

        private void HandleClientComm(object client)
        {
            TcpClient tcpClient = (TcpClient)client;
            NetworkStream localClientStream = null;

            try
            {
                string systemCode = this.txtSystemCode.Text;
                int code = int.Parse(systemCode);
                systemCode = code.ToString("X2");

                DisableButton(btnTcpStart, false);
                localClientStream = tcpClient.GetStream();

                IPAddress clientIP = ((IPEndPoint)tcpClient.Client.RemoteEndPoint).Address;
                int clientPort = ((IPEndPoint)tcpClient.Client.RemoteEndPoint).Port;

                byte[] message = new byte[4096];
                int bytesRead;

                while (tcpClient.Connected && localClientStream != null)
                {
                    bytesRead = 0;
                    try
                    {
                        bytesRead = localClientStream.Read(message, 0, 4096);
                    }
                    catch
                    {
                        break;
                    }

                    if (bytesRead == 0)
                    {
                        break;
                    }

                    // 将接收到的消息转换为字符串
                    string clientMessage = BitConverter.ToString(message, 0, bytesRead).Replace("-", "");
                    AppendLog($"来自集中告警系统{clientIP}:{clientPort}的消息: " + clientMessage, txtLogRecive);
                    //告警信息同步

                    // 使用localClientStream而不是全局clientStream
                    if (clientMessage.Equals("FAFAFA" + systemCode))
                    {
                        if (this.cboxAlarmReportFlag.Checked)
                        {
                            //告警同步请求指令
                            byte[] alarmAsyncMsgByte = StringToByteArray("FAFAFA" + systemCode + "0000");
                            localClientStream.Write(alarmAsyncMsgByte, 0, alarmAsyncMsgByte.Length);
                            Log.Information($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000");
                            AppendLog($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000", txtLogSend);
                            // 告警同步结束主动通知
                            byte[] alarmAsyncFinishMsgByte = StringToByteArray("F0F0F0" + systemCode);
                            localClientStream.Write(alarmAsyncFinishMsgByte, 0, alarmAsyncFinishMsgByte.Length);
                            Log.Information($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000");
                            AppendLog($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000", txtLogSend);
                        }
                        else if (_mqttClient == null)
                        {
                            AppendLog($"Warn:MQTT服务未连接", txtLogSend);
                            //告警同步请求指令
                            byte[] alarmAsyncMsgByte = StringToByteArray("FAFAFA" + systemCode + "0000");
                            localClientStream.Write(alarmAsyncMsgByte, 0, alarmAsyncMsgByte.Length);
                            Log.Information($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000");
                            AppendLog($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000", txtLogSend);
                            // 告警同步结束主动通知
                            byte[] alarmAsyncFinishMsgByte = StringToByteArray("F0F0F0" + systemCode);
                            localClientStream.Write(alarmAsyncFinishMsgByte, 0, alarmAsyncFinishMsgByte.Length);
                            Log.Information($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000");
                            AppendLog($"给集中告警{clientIP}:{clientPort}回应报文：FAFAFA" + systemCode + "0000", txtLogSend);
                        }
                        else
                        {
                            _mqttClient.PublishAsync(MqttClientTool.MQ_TOPIC_CSHARP_CLIENT_ALERT_ASYNC, MqttClientTool.asyncFlag);
                            AppendLog($"发布到主题:{MqttClientTool.MQ_TOPIC_CSHARP_CLIENT_ALERT_ASYNC}", txtLogInfo);
                        }
                    }
                    else if (clientMessage.Equals("FFFFFF" + systemCode)) //心跳指令
                    {
                        // 向集中告警回应心跳
                        byte[] heartBeanMsgByte = StringToByteArray(clientMessage);
                        localClientStream.Write(heartBeanMsgByte, 0, heartBeanMsgByte.Length);
                        Log.Information($"给集中告警{clientIP}:{clientPort}回应心跳：{clientMessage}");
                        AppendLog($"给集中告警{clientIP}:{clientPort}回应心跳：{clientMessage}", txtLogSend);
                    }
                    else
                    {
                        Log.Information($"未知报文信息: {clientMessage}");
                        AppendLog($"未知报文信息:{clientMessage}", txtLogSend);
                    }

                    localClientStream.Flush();
                }
            }
            catch (Exception ex)
            {
                Log.Error($"处理客户端通信时发生错误: {ex.Message}");
                AppendLog($"处理客户端通信时发生错误: {ex.Message}", txtLogInfo);
            }
            finally
            {
                // 从活跃连接列表中移除
                lock (clientLock)
                {
                    activeClients.Remove(tcpClient);
                }

                // 确保资源被正确释放
                if (localClientStream != null)
                {
                    try
                    {
                        localClientStream.Close();
                        localClientStream.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"关闭客户端流时发生错误: {ex.Message}");
                    }
                }

                if (tcpClient != null)
                {
                    try
                    {
                        tcpClient.Close();
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"关闭TCP客户端时发生错误: {ex.Message}");
                    }
                }

                AppendLog($"客户端已断开连接.", txtLogInfo);
            }
        }

        #endregion

        private void DisableButton(Control button, bool enable)
        {
            if (button.InvokeRequired) // 判断是否在主线程上执行
            {
                button.Invoke(new ControlAction(DisableButton), new object[] { button, enable }); // 发送请求
            }
            else
            {
                button.Enabled = enable; // 在主线程上执行
            }
        }

        private byte[] StringToByteArray(string hexString)
        {
            int length = hexString.Length;
            byte[] byteArray = new byte[length / 2];

            for (int i = 0; i < length; i += 2)
            {
                byteArray[i / 2] = Convert.ToByte(hexString.Substring(i, 2), 16);
            }

            return byteArray;
        }

        private static IPAddress GetLocalIpAddress()
        {
            var host = Dns.GetHostEntry(Dns.GetHostName());

            foreach (var ip in host.AddressList)
            {
                if (ip.AddressFamily == AddressFamily.InterNetwork)
                {
                    return ip;
                }
            }

            throw new Exception("本机未连接到局域网！");
        }

        private void AppendLog(string log, RichTextBox textBox, bool isHex = false)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => AppendLog(log, textBox, isHex)));
            }
            else
            {
                if (isHex)
                {
                    // 将16进制字符串转换成字节数组，并在日志框中显示
                    byte[] bytes = log.Trim().Split().Select(s => Convert.ToByte(s, 16)).ToArray();
                    textBox.AppendText($"{DateTime.Now.ToString("yyyyMMdd HH:mm:ss")} 接收到数据（16进制）：{log}{Environment.NewLine}");
                    textBox.AppendText($"{DateTime.Now.ToString("yyyyMMdd HH:mm:ss")} 接收到数据（ASCII）：{System.Text.Encoding.ASCII.GetString(bytes)}{Environment.NewLine}");
                }
                else
                {
                    textBox.AppendText($"{DateTime.Now.ToString("yyyyMMdd HH:mm:ss")} {log}{Environment.NewLine}");
                }
            }
        }

        #region ConnectToMqttServer =>连接MQTT 当连接成功之后才会连接集中告警服务TCP

        private async Task ConnectToMqttServer()
        {
            if (_mqttClient == null)
            {
                _mqttClient = new MqttClientTool();
            }

            string ip = IniFileUtils.ReadValue(configFileName, iniGroupNameMQ, CONFIG_MQTT_IP);
            string port = IniFileUtils.ReadValue(configFileName, iniGroupNameMQ, CONFIG_MQTT_PORT);
            string type = IniFileUtils.ReadValue(configFileName, iniGroupNameMQ, CONFIG_MQTT_TYPE);
            int _port;
            int.TryParse(port, out _port);

            Log.Information($"[MQTT]获取配置文件MQ的ip地址为:{ip}");
            Log.Information($"[MQTT]获取配置文件MQ的port地址为:{_port}");
            AppendLog($"[MQTT]MQTT_IP:{ip}", txtLogInfo);
            AppendLog($"[MQTT]MQTT_PORT:{port}", txtLogInfo);

            await _mqttClient.ConnectAsync(ip, _port, "EMQX_ALARM_SERVER_" + type.ToUpper());

            await _mqttClient.SubscribeAsync(MqttClientTool.MQ_TOPIC_CSHARP_CLIENT + "#");
            Log.Information("[MQTT]连接成功");
            AppendLog("[MQTT]连接成功", txtLogInfo);

            #region MQTT服务启动完毕之后，然后再尝试启动TEL服务 20231106 成都修改，将之前的同步启动更换成了后置启动

            loadSystemConfig();
            Log.Information("[ini]系统配置加载完毕");
            AppendLog("[ini]系统配置加载完毕", txtLogInfo);

            TcpServerStartPreCheck();

            this.timerAutoClearLogBox.Start();
            Log.Information("[Panel]自动清除日志已开启");
            AppendLog("[Panel]自动清除日志已开启", txtLogInfo);

            logUtils.LogConfig.ConfigureLogger("TEL_alarmLog");

            this.txtLogInfo.ReadOnly = true;
            this.txtLogRecive.ReadOnly = true;
            this.txtLogSend.ReadOnly = true;
            if (cboxAutoRestart.Checked)
            {
                autoRestartCheck();
                Log.Information("[Panel]sfsystem自动重启已开启");
                AppendLog("[Panel]sfsystem自动重启已开启", txtLogInfo);
            }

            #endregion

            _mqttClient.MessageReceived += (sender, args) =>
            {
                if (args.Topic.StartsWith(MqttClientTool.MQ_TOPIC_CSHARP_CLIENT_START))
                {
                    AppendLog("告警信息同步开始>>>>>>>>>>", txtLogSend);
                }
                else if (args.Topic.StartsWith(MqttClientTool.MQ_TOPIC_CSHARP_CLIENT_END))
                {
                    AppendLog("告警信息同步结束>>>>>>>>>>", txtLogSend);
                }
                else if (args.Topic.StartsWith(MqttClientTool.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO))
                {
                    // 将MQTT消息转发给所有连接的TCP客户端
                    ForwardMqttMessageToTcpClients(args.Topic, args.Payload);
                }
                else
                {
                    AppendLog($"其他信息>>>>>>>>>>{args.Topic}:{args.Payload}", txtLogSend);
                }
            };
        }

        /// <summary>
        /// 将MQTT消息转发给所有连接的TCP客户端
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <param name="payload">消息内容</param>
        private void ForwardMqttMessageToTcpClients(string topic, string payload)
        {
            try
            {
                Thread.Sleep(10);
                if (!this.cboxAlarmReportFlag.Checked)
                {
                    lock (clientLock)
                    {
                        AppendLog($"[转发]当前TCP客户端连接数: {activeClients.Count}", txtLogSend);

                        // 创建要移除的客户端列表
                        var clientsToRemove = new List<TcpClient>();

                        foreach (var tcpClient in activeClients)
                        {
                            try
                            {
                                if (tcpClient != null && tcpClient.Connected)
                                {
                                    IPAddress clientIP = ((IPEndPoint)tcpClient.Client.RemoteEndPoint).Address;
                                    int clientPort = ((IPEndPoint)tcpClient.Client.RemoteEndPoint).Port;

                                    NetworkStream stream = tcpClient.GetStream();
                                    byte[] messageBytes = StringToByteArray(payload);
                                    stream.Write(messageBytes, 0, messageBytes.Length);
                                    stream.Flush();
                                    AppendLog($"[转发]已向 {clientIP}:{clientPort} 转发消息: {payload}", txtLogSend);
                                }
                                else
                                {
                                    // 标记断开的客户端以便移除
                                    clientsToRemove.Add(tcpClient);
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"[转发]向TCP客户端转发消息时发生错误: {ex.Message}");
                                AppendLog($"[转发]向TCP客户端转发消息时发生错误: {ex.Message}", txtLogSend);
                                // 标记出错的客户端以便移除
                                clientsToRemove.Add(tcpClient);
                            }
                        }

                        // 移除断开或出错的客户端
                        foreach (var clientToRemove in clientsToRemove)
                        {
                            activeClients.Remove(clientToRemove);
                            try
                            {
                                clientToRemove?.Close();
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"[转发]关闭断开的客户端时发生错误: {ex.Message}");
                            }
                        }

                        if (clientsToRemove.Count > 0)
                        {
                            AppendLog($"[转发]已清理 {clientsToRemove.Count} 个断开的TCP客户端连接", txtLogSend);
                        }
                    }
                }
                else
                {
                    AppendLog("[转发]告警报告标志已开启，跳过消息转发", txtLogSend);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[转发]转发MQTT消息到TCP客户端时发生错误: {ex.Message}");
                AppendLog($"[转发]转发MQTT消息到TCP客户端时发生错误: {ex.Message}", txtLogSend);
            }
        }

        #endregion

        private void btnTELogClear_Click(object sender, EventArgs e)
        {
            this.txtLogInfo.Clear();
            this.txtLogSend.Clear();
            this.txtLogRecive.Clear();
        }

        private void cboxAlarmReportFlag_CheckedChanged(object sender, EventArgs e)
        {
            if (cboxAlarmReportFlag.Checked)
            {
                AppendLog("Open", txtLogInfo);
            }
            else
            {
                AppendLog("Close", txtLogInfo);
            }
        }

        private void cboxTELAutoRun_CheckedChanged(object sender, EventArgs e)
        {
            AppendLog("[TCP服务自启动配置变动],请保存配置!", txtLogInfo);
        }

        private void writeConfigToFile(Configuration config)
        {
            if (config == null)
            {
                return;
            }

            using (StreamWriter sw = new StreamWriter(configFileName))
            {
                var settings = config.AppSettings.Settings;
                foreach (string key in settings.AllKeys)
                {
                    sw.WriteLine($"{key}={settings[key].Value}");
                }
            }
        }

        private void btnSaveConfig_Click(object sender, EventArgs e)
        {
            string ip = this.txtIpAddress.Text;
            string port = this.txtPort.Text;
            string systemCode = this.txtSystemCode.Text;
            bool rememberChecked = this.cboxSaveIp.Checked;
            bool tcpAutoRunChecked = this.cboxTELAutoRun.Checked;
            bool tcpAlarmReport = this.cboxAlarmReportFlag.Checked;

            if (systemCode == null || systemCode.Equals(""))
            {
                AppendLog("SystemCode不能为空", txtLogInfo);
                return;
            }


            string v = "";
            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_IP, ip);
            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_PORT, port);
            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_ALARM_SYSTEMCODE, systemCode);
            if (rememberChecked)
            {
                v = "true";
            }
            else
            {
                v = "false";
            }

            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_SAVEIP, v);

            v = "";
            if (tcpAutoRunChecked)
            {
                v = "true";
            }
            else
            {
                v = "false";
            }

            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_AUTO_RUN_SERVER, v);

            v = "";
            if (tcpAlarmReport)
            {
                v = "true";
            }
            else
            {
                v = "false";
            }

            IniFileUtils.WriteValue(configFileName, iniGroupNameTEL, CONFIG_KEY_ALARM_REPORT, v);
            AppendLog($"IP{ip},PORT{port}已记入配置文件,下次将自动采用当前配置信息!", txtLogInfo);
        }

        private void cboxSaveIp_CheckedChanged(object sender, EventArgs e)
        {
            AppendLog("[IP端口记忆功能变更],请保存配置项!", txtLogInfo);
        }

        private void linklabelTcpClientCount_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
        }

        private void linkLabel21_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            txtLogInfo.Clear();
        }

        private void linkLabel22_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            txtLogRecive.Clear();
        }

        private void linkLabel23_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            txtLogSend.Clear();
        }

        private void killProcess(Process process)
        {
            process.Kill();
        }

        private void timerTCPClientCount_Tick(object sender, EventArgs e)
        {
            txtLogInfo.Clear();
            txtLogRecive.Clear();
            txtLogSend.Clear();
        }

        private void timerLogClear_Tick(object sender, EventArgs e)
        {
            this.rboxResult.Clear();
            this.rtextboxErrorResult.Clear();
        }

        private void timerAutoRestart_Tick(object sender, EventArgs e)
        {
            Process[] psStart = Process.GetProcessesByName("sftx-starter");
            Process[] psJava = Process.GetProcessesByName("java");

            if (psStart.Length > 0 || psJava.Length > 0)
            {
                try
                {
                    foreach (Process p in psStart)
                    {
                        killProcess(p);
                    }

                    foreach (Process p in psJava)
                    {
                        killProcess(p);
                    }

                    rboxResult.Clear();
                    rtextboxErrorResult.Clear();
                    rboxResult.Text = "程序已终止.";
                }
                catch (Exception ex)
                {
                    this.Invoke(ReadErrOutput, ex.Message);
                }
            }

            Thread.Sleep(2000);
            string argStr = "java -jar sftx-server.jar";
            string serverDirPath = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            Excute(serverDirPath, GConfig.INDOOR_SYSTEM_SERVER_NAME, argStr);

            string time = DateTime.Now.ToString("f");
            labelAutoRestartMsg.Text = "程序于：" + time + "重启完毕!";
        }

        private void autoRestartCheck()
        {
            DateTime now = DateTime.Now;
            TimeSpan timeUntilTarget = new DateTime(now.Year, now.Month, now.Day, 04, 0, 0) - now;
            if (timeUntilTarget < TimeSpan.Zero)
            {
                // 如果当前时间已经超过了每天凌晨4点，则明天再执行
                timeUntilTarget = timeUntilTarget.Add(TimeSpan.FromDays(1));
            }

            restartTimer = new System.Timers.Timer(timeUntilTarget.TotalMilliseconds);
            restartTimer.Elapsed += Timer_Elapsed;
            restartTimer.AutoReset = false; // 设置为 false，保证只触发一次
            restartTimer.Start();
        }

        //private void autoRestartCheck()
        //{
        //    // 设置为每2分钟执行一次
        //    TimeSpan interval = TimeSpan.FromMinutes(2);

        //    // 创建定时器
        //    restartTimer = new System.Timers.Timer();
        //    restartTimer.Interval = interval.TotalMilliseconds;
        //    restartTimer.Elapsed += Timer_Elapsed;
        //    restartTimer.AutoReset = true; // 设置为 true，使定时器重复执行
        //    restartTimer.Start();
        //}

        void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            Process[] psStart = Process.GetProcessesByName("sftx-starter");
            Process[] psJava = Process.GetProcessesByName("java");

            if (psStart.Length > 0 || psJava.Length > 0)
            {
                try
                {
                    foreach (Process p in psStart)
                    {
                        killProcess(p);
                    }

                    foreach (Process p in psJava)
                    {
                        killProcess(p);
                    }

                    rboxResult.Clear();
                    rtextboxErrorResult.Clear();
                    rboxResult.Text = "程序已终止.";
                }
                catch (Exception ex)
                {
                    this.Invoke(ReadErrOutput, ex.Message);
                }
            }

            Thread.Sleep(2000);


            string serverDirPath = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            // Excute(serverDirPath, GConfig.INDOOR_SYSTEM_SERVER_NAME, argStr);  // 加密程序启动
            Excute(currentPathWithoutAppName + GConfig.PATH_HOME_JAVA_ENV + "/bin/", "java.exe", " -jar " + serverDirPath + "sftx-server.jar"); //jar包直接启动

            //string argStr = "java -jar sftx-server.jar";
            //string serverDirPath = currentPathWithoutAppName + GConfig.PATH_INDOOR_SYSTEM_SERVER_HOME;
            //Excute(serverDirPath, GConfig.INDOOR_SYSTEM_SERVER_NAME, argStr);

            string time = DateTime.Now.ToString("f");
            labelAutoRestartMsg.Text = "程序于：" + time + "重启完毕!";
            restartTimer.Interval = TimeSpan.FromDays(1).TotalMilliseconds;
            restartTimer.Start();
        }

        private void cboxAutoRestart_CheckedChanged(object sender, EventArgs e)
        {
            if (cboxAutoRestart.Checked)
            {
                this.cboxAutoRestart.Text = "当前已启用，取消勾选后将关闭";
                if (restartTimer == null)
                {
                    MessageBox.Show("服务已重启.");
                    autoRestartCheck();
                }
            }
            else
            {
                this.cboxAutoRestart.Text = "当前已关闭，勾选后启动";
                if (restartTimer != null)
                {
                    MessageBox.Show("已关闭服务自动重启.");
                    restartTimer.AutoReset = false;
                    restartTimer.Enabled = false;
                    restartTimer.Stop();
                    restartTimer.Dispose();
                    restartTimer = null;
                }
            }
        }


        #region GetJavaProcessId => 获取启动的java进程

        private int GetJavaProcessId(String cxmdString)
        {
            //java -jar sftx-server.jar
            ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT ProcessId, CommandLine FROM Win32_Process WHERE Name='java.exe'");
            foreach (ManagementObject process in searcher.Get())
            {
                string commandLine = process["CommandLine"].ToString();
                if (commandLine.EndsWith("sftx-server.jar"))
                {
                    int javaProcessId = Convert.ToInt32(process["ProcessId"]);
                    return javaProcessId;
                }
            }

            return -1;
        }

        #endregion
    }
}