using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CCPanel.shell
{
    internal class CShell
    {

        public static string ExcuteCmd(string WorkingDirectory, string startFileName, string startFileArg)
        {
            string msg ;
            using (Process CmdProcess = new Process()) { 
                CmdProcess.StartInfo.WorkingDirectory = WorkingDirectory;//工作目录
                CmdProcess.StartInfo.FileName = WorkingDirectory + startFileName;      // 命令
                CmdProcess.StartInfo.Arguments = startFileArg;      // 参数
                CmdProcess.StartInfo.CreateNoWindow = true;         // 不创建新窗口
                CmdProcess.StartInfo.UseShellExecute = false;
                CmdProcess.StartInfo.RedirectStandardInput = true;  // 重定向输入
                CmdProcess.StartInfo.RedirectStandardOutput = true; // 重定向标准输出
                CmdProcess.StartInfo.RedirectStandardError = true;  // 重定向错误输出
                CmdProcess.StartInfo.Verb = "runas";
                CmdProcess.Start();
                CmdProcess.StandardInput.AutoFlush = true;
                msg = CmdProcess.StandardOutput.ReadToEnd();
                CmdProcess.WaitForExit();
                CmdProcess.Close();
                return msg;
            }
        }







    }



}

