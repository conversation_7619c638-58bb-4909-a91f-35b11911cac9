<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{863F5D91-632A-42D8-B23F-9126EB00ED77}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>CCPanel</RootNamespace>
    <AssemblyName>CCPanel</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>E:\室分系统发布包\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>1</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>CCPanel.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>maplestory.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>BBEAE9B4262D1A6D914C959FD6B0CE589EB379CA</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>CCPanel_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MQTTnet, Version=3.0.15.0, Culture=neutral, PublicKeyToken=b69712f52770c0a7, processorArchitecture=MSIL">
      <HintPath>..\TcpServer\packages\MQTTnet.3.0.15\lib\net452\MQTTnet.dll</HintPath>
    </Reference>
    <Reference Include="Serilog, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.2.5.0\lib\net46\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Formatting.Compact, Version=1.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Formatting.Compact.1.0.0\lib\net45\Serilog.Formatting.Compact.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Console, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.Console.2.1.0\lib\net45\Serilog.Sinks.Console.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.File.4.0.0\lib\net45\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.PeriodicBatching, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.PeriodicBatching.2.1.1\lib\net45\Serilog.Sinks.PeriodicBatching.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.RollingFile, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.RollingFile.2.0.0\lib\net45\Serilog.Sinks.RollingFile.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Seq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.Seq.4.0.0\lib\net45\Serilog.Sinks.Seq.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.ServiceProcess">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Data">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Deployment">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Drawing">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Net.Http">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Xml">
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="FrmMainPanel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmMainPanel.Designer.cs">
      <DependentUpon>FrmMainPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmUrlConfigPage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmUrlConfigPage.Designer.cs">
      <DependentUpon>FrmUrlConfigPage.cs</DependentUpon>
    </Compile>
    <Compile Include="logUtils\LogConfig.cs" />
    <Compile Include="mqtt\MqttClientTool.cs" />
    <Compile Include="mqtt\MqttMessageEventArgs.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ClientManager.cs" />
    <Compile Include="utils\GConfig.cs" />
    <Compile Include="utils\IniFileUtils.cs" />
    <Compile Include="utils\ProcessUtils.cs" />
    <Compile Include="utils\SCUtils.cs" />
    <Compile Include="utils\GlobalUtils.cs" />
    <Compile Include="shell\CShell.cs" />
    <EmbeddedResource Include="FrmMainPanel.resx">
      <DependentUpon>FrmMainPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmUrlConfigPage.resx">
      <DependentUpon>FrmUrlConfigPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="CCPanel_TemporaryKey.pfx" />
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="maplestory.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>