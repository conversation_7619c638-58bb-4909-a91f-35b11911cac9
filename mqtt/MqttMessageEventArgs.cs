using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TcpServer
{
    public class MqttMessageEventArgs : EventArgs
    {
        public string Topic { get; }
        public string Payload { get; }

        public MqttMessageEventArgs(string topic, string payload)
        {
            Topic = topic;
            Payload = payload;
        }
    }
}
