using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Client.Connecting;
using MQTTnet.Client.Options;
using MQTTnet.Exceptions;
using Serilog;

namespace TcpServer
{
    internal class MqttClientTool
    {

        private bool isConnected = false;
        private bool isConnecting = false;

        private readonly IMqttClient _mqttClient;
        public event EventHandler<MqttMessageEventArgs> MessageReceived;

        // public static String MQ_TOPIC_BASE = "sf/device/";
        // public static String MQ_TOPIC_DEVICE_ONLINE = MQ_TOPIC_BASE + "online/";
        // public static String MQ_TOPIC_DEVICE_OFFLINE = MQ_TOPIC_BASE + "offline/";
        //
        // public static String MQ_TOPIC_DEVICE_PROBE_SIGNAL_STRENGTH = MQ_TOPIC_BASE + "signalStrength/";
        //
        // public static String MQ_TOPIC_DEVICE_LINK_ERR = MQ_TOPIC_BASE + "link/err";
        // public static String MQ_TOPIC_DEVICE_LINK_ONLINE = MQ_TOPIC_BASE + "link/online";
        //
        // public static String MQ_TOPIC_PROBE_ERR = MQ_TOPIC_BASE + "prbe/error/";
        // public static String MQ_TOPIC_PROBE_ONLINE = MQ_TOPIC_BASE + "prbe/online/";


        public static String MQ_TOPIC_CSHARP_CLIENT = "sf/alert/";
        public static String MQ_TOPIC_CSHARP_CLIENT_ALERT_ASYNC = MQ_TOPIC_CSHARP_CLIENT + "async";
        public static String MQ_TOPIC_CSHARP_CLIENT_ALERT_COUNT = MQ_TOPIC_CSHARP_CLIENT + "count";
        public static String MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO = MQ_TOPIC_CSHARP_CLIENT + "info";

        public static String asyncFlag = "1";

        public static String MQ_TOPIC_CSHARP_CLIENT_START = MQ_TOPIC_CSHARP_CLIENT + "start";
        public static String MQ_TOPIC_CSHARP_CLIENT_END = MQ_TOPIC_CSHARP_CLIENT + "end";

        public MqttClientTool()
        {
            CCPanel.logUtils.LogConfig.ConfigureLogger("SF_mqtt");
            _mqttClient = new MqttFactory().CreateMqttClient();
        }

        public async Task ConnectAsync(string brokerHostName, int brokerPort, string clientId)
        {
            if (isConnected || isConnecting) return;
            var options = new MqttClientOptionsBuilder()
                .WithTcpServer(brokerHostName, brokerPort)
                .WithClientId(clientId)
                .WithCredentials("indoorSystem", "indoorSystem")
                .WithCleanSession(true)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(30))
                .Build();


            while (!isConnected && !isConnecting) {
                try
                {
                    isConnecting = true;
                    MqttClientAuthenticateResult t = await _mqttClient.ConnectAsync(options, System.Threading.CancellationToken.None);
                    
                    isConnected = true;
                }
                catch (MqttCommunicationException ex)
                {
                    Log.Error(ex, "[MQTT][MqttCommunicationException]连接异常!" + ex.Message);
                    //throw;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "[MQTT][Exception]连接异常" + ex.Message);
                    //throw;
                }
                finally
                {
                    isConnecting = false;
                }
                await Task.Delay(2000); // Wait 1 seconds before retrying
            }




            _mqttClient.UseApplicationMessageReceivedHandler(e =>
            {
                Log.Information($"接收到来自Topic的消息: {e.ApplicationMessage.Topic}:{e.ApplicationMessage.ConvertPayloadToString()}");
                var args = new MqttMessageEventArgs(e.ApplicationMessage.Topic, e.ApplicationMessage.ConvertPayloadToString());
                MessageReceived?.Invoke(this, args);
            });

            _mqttClient.UseDisconnectedHandler(async e =>
            {
                Log.Warning("[MQTT]服务器断开连接!");
                isConnected = false;
                while (!isConnected && !isConnecting)
                {
                    try
                    {
                        isConnecting = true;
                        await _mqttClient.ConnectAsync(options, System.Threading.CancellationToken.None);
                    }
                    catch (MqttCommunicationException ex)
                    {
                        Log.Error(ex, "[MQTT]重连失败!");
                    }
                    finally
                    {
                        isConnecting = false;
                    }
                    await Task.Delay(1000); // Wait 1 seconds before retrying
                }
            });

            _mqttClient.UseConnectedHandler(async e =>
            {
                isConnected = true;
                Log.Information("[MQTT]服务器建立连接成功!");
            });

        }

        public async Task SubscribeAsync(string topic)
        {
            var topicFilter = new MqttTopicFilterBuilder()
                .WithTopic(topic)
                .Build();
            await _mqttClient.SubscribeAsync(topicFilter);
            Log.Information($"订阅Topic: {topic}");
        }

        public async Task PublishAsync(string topic, string payload, bool retained = false)
        {
            var message = new MqttApplicationMessageBuilder()
                            .WithTopic(topic)
                            .WithPayload(payload)
                            .WithRetainFlag(retained) // 设置消息的保留标志
                            .Build();

            await _mqttClient.PublishAsync(message);

            if (retained)
            {
                Log.Information($"发布消息到Topic: '{topic}' 作为遗嘱消息.'{payload}'");
            }
            else
            {
                Log.Information($"发布消息到Topic: '{topic}'消息:'{payload}'");
            }
        }

        public async Task PublishAsync(string topic, string message)
        {
            var mqttMessage = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(message)
                .WithExactlyOnceQoS()
                .WithRetainFlag()
                .Build();

            await _mqttClient.PublishAsync(mqttMessage);
            Log.Information($"发布消息到Topic: '{topic}': {message}");
        }

        public async Task DisconnectAsync()
        {
            await _mqttClient.DisconnectAsync();
            Log.Information("与服务断开连接");
        }
    }
}
