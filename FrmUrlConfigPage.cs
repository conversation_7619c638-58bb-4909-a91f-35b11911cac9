using CCPanel.utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CCPanel
{
    public partial class FrmUrlConfigPage : Form
    {

        private static string currentPathWithoutAppName = System.Environment.CurrentDirectory;
        private string configFileName = currentPathWithoutAppName + "/CPanelConfig.ini";

       
        public static string CONFIG_URL_CONFIG = "LOG_PATH";
        public static string CONFIG_URL_SF_SFSYSTEM_LOG = "SF_SYSTEM_LOG";
        public static string CONFIG_URL_SF_EMQX_LOG = "SF_EMQX_LOG";
        public static string CONFIG_URL_SF_TEL_LOG = "SF_TEL_LOG";
        


        public FrmUrlConfigPage()
        {
            InitializeComponent();
            FormInit();
            ConfigFileInit();
        }

        private void FormInit() { 
            this.lbBaseUrl.Text = currentPathWithoutAppName + utils.GConfig.PATH_HOME_SOFTWARE;
        }

        private void ConfigFileInit()
        {
            if (File.Exists(configFileName))
            {
                String sf_system_log_path = IniFileUtils.ReadValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_SFSYSTEM_LOG);
                if (sf_system_log_path == null || sf_system_log_path.Trim().Length == 0)
                {
                    String defaultPath = "";
                    IniFileUtils.WriteValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_SFSYSTEM_LOG, defaultPath);
                    sf_system_log_path = "";
                }
                else { 
                    this.tboxSfLogPath.Text = sf_system_log_path.Trim();
                }

                String sf_emqx_log_path = IniFileUtils.ReadValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_EMQX_LOG);
                if (sf_emqx_log_path == null || sf_emqx_log_path.Trim().Length == 0)
                {
                    IniFileUtils.WriteValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_EMQX_LOG, "");
                    sf_emqx_log_path = "";
                }
                else { 
                    this.tboxEmqxLogPath.Text = sf_emqx_log_path.Trim();
                }

                String sf_tel_log_path = IniFileUtils.ReadValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_TEL_LOG);
                if (sf_tel_log_path == null || sf_tel_log_path.Trim().Length == 0)
                {
                    IniFileUtils.WriteValue(configFileName, CONFIG_URL_CONFIG, CONFIG_URL_SF_TEL_LOG, "");
                    sf_tel_log_path = "";
                }
                else { 
                    this.tboxTelLogPath.Text = sf_tel_log_path.Trim();
                }
            }
            else
            {
               
            }
        }
    }
}
