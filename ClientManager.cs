using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CCPanel
{
    class ClientManager
    {
        private static readonly object lockObject = new object();
        private static Queue<TcpClient> clientPool = new Queue<TcpClient>();

        // 添加客户端连接到连接池
        public static void AddClient(TcpClient client)
        {
            lock (lockObject)
            {
                clientPool.Enqueue(client);
            }
        }

        public static void RemoveClient(TcpClient client)
        {
            lock (lockObject)
            {
                clientPool = new Queue<TcpClient>(clientPool.Where(c => c != client));
            }
        }

        public static int ClientOnlineCount()
        {
            return clientPool.Count();
        }
    }
}
