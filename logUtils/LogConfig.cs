using Serilog.Events;
using Serilog;
using System;
using System.IO;
using System.Linq;
using Serilog.Formatting.Json;

namespace CCPanel.logUtils
{
    internal class LogConfig
    {

        public LogConfig() {
        }

        public static void ConfigureLogger(string logFileName)
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Verbose()
                .WriteTo.Console()
                .WriteTo.File(
                    new JsonFormatter(),
                    GetLogFilePath(logFileName),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 180,
                    fileSizeLimitBytes: 50*1024*1024, // 50 MB
                    rollOnFileSizeLimit: true,
                    shared:true
                    )
                .CreateLogger();

            Log.Information("Logger initialized.");
        }

        private static string GetLogFilePath(string logFileNameIn)
        {
            var logDirectory = AppDomain.CurrentDomain.BaseDirectory + "logs";
            var logFileName = $"{logFileNameIn}-{DateTime.Now:yyyyMMdd}.log";
            return $"{logDirectory}\\{logFileName}";
        }



    }
}
