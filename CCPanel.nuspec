<?xml version="1.0" encoding="utf-8"?>
<package >
  <metadata>
    <id>$id$</id>
    <version>$version$</version>
    <title>$title$</title>
    <authors>$author$</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <!-- <icon>icon.png</icon> -->
    <projectUrl>http://project_url_here_or_delete_this_line/</projectUrl>
    <description>$description$</description>
    <releaseNotes>Summary of changes made in this release of the package.</releaseNotes>
    <copyright>$copyright$</copyright>
    <tags>Tag1 Tag2</tags>
	
		
    <dependencies>
	  <dependency id="MQTTnet" version="3.0.15" />
	  <dependency id="Serilog" version="2.0.0" />
	  <dependency id="Serilog.Sinks.Console" version="2.1.0" />
	  <dependency id="Serilog.Sinks.File" version="2.0.0" />
    </dependencies>
	
	
  </metadata>
</package>