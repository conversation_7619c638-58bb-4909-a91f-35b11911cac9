using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.ServiceProcess;
using System.Diagnostics;
using System.Windows.Forms;
using CCPanel.utils;

namespace CCPanel.shell
{
    internal class SCUtils
    {
        public static bool ServiceCheckExist(string serviceName)
        {
            ServiceController[] services = ServiceController.GetServices();
            foreach (ServiceController service in services)
            {
                if (service.ServiceName.ToLower() == serviceName.ToLower())
                {
                    return true;
                }
            }
            return false;
        }

        public static bool ServiceStart(string serviceName)
        {
            bool exist = ServiceCheckExist(serviceName);
            if (exist)
            {
                ServiceController sc = new ServiceController(serviceName, ".");
                if (sc.Status == System.ServiceProcess.ServiceControllerStatus.Stopped)
                {
                    sc.Start();
                    sc.WaitForStatus(System.ServiceProcess.ServiceControllerStatus.Running);
                    sc.Refresh();
                    return true;
                }
            }
            return false;
        }

        public static bool ServiceStop(string serviceName)
        {
            bool exist = ServiceCheckExist(serviceName);
            if (exist)
            {
                using (ServiceController sc = new ServiceController(serviceName, ".")) {
                    if (sc.Status == System.ServiceProcess.ServiceControllerStatus.Running)
                    {
                        if (sc.CanStop)
                        {
                            sc.Stop();
                            sc.WaitForStatus(System.ServiceProcess.ServiceControllerStatus.Stopped);
                            sc.Refresh();
                            return true;
                        }
                    }
                }

            }
            return false;
        }


        public static bool ExcuteByCShell(string serviceName,string WorkingDirectory, string startFileName, string argument) {
            bool exist = GlobalUtils.IsfileExist(WorkingDirectory + startFileName);
            if (!exist)
            {
                return false;
            }
            bool serviceExist = GlobalUtils.IsServiceExisted(serviceName);
            if (!exist)
            {
                return false;
            }
            CShell.ExcuteCmd(WorkingDirectory, startFileName, argument);
            return true;
        }
    }
}
