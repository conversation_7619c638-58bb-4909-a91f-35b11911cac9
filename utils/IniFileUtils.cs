using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace CCPanel.utils
{
    internal class IniFileUtils
    {
        private static readonly Encoding encoding = Encoding.UTF8;
        private static readonly char[] keyValueSeparator = { '=' };
        private static readonly char[] sectionStartChars = { '[' };
        private static readonly char[] sectionEndChars = { ']' };
        private static readonly int MaxBufferSize = 32767; // 32K，最大缓存长度

        // 写入配置项
        public static void WriteValue(string fileName, string sectionName, string keyName, string value)
        {
            StringBuilder sb = new StringBuilder(MaxBufferSize);
            GetPrivateProfileString(sectionName, null, "", sb, MaxBufferSize, fileName);

            if (!sb.ToString().Contains(keyName))
            {
                // 新增 key，需要添加 section 和 key
                WritePrivateProfileString(sectionName, keyName, value, fileName);
            }
            else
            {
                // 更新 value
                WritePrivateProfileString(sectionName, keyName, value, fileName);
            }
        }

        // 读取配置项
        public static string ReadValue(string fileName, string sectionName, string keyName, string defaultValue = "")
        {
            StringBuilder sb = new StringBuilder(MaxBufferSize);
            GetPrivateProfileString(sectionName, keyName, defaultValue, sb, MaxBufferSize, fileName);
            return sb.ToString();
        }

        // 释放资源
        public static void Release(IntPtr ptr)
        {
            Marshal.FreeCoTaskMem(ptr);
        }

        #region 调用 kernel32.dll 中的方法
        [DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool WritePrivateProfileString(string lpAppName, string lpKeyName, string lpString, string lpFileName);

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int GetPrivateProfileString(string lpAppName, string lpKeyName, string lpDefault, StringBuilder lpReturnedString, int nSize, string lpFileName);
        #endregion
    }
}
