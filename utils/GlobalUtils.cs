using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.ServiceProcess;
using System.Diagnostics;
using System.Windows.Forms;

namespace CCPanel.shell
{
    internal class GlobalUtils
    {
        public static bool IsfileExist(string fileFullPath)
        {
            if (!File.Exists(fileFullPath))
            {
                return false;
            }
            return true;
        }

        public static bool IsServiceExisted(string serviceName)
        {
            ServiceController[] services = ServiceController.GetServices();
            foreach (ServiceController service in services)
            {
                if (service.ServiceName.ToLower() == serviceName.ToLower())
                {
                    return true;
                }
            }
            return false;
        }

        public static void KillProcess(string processName)
        {
            Process[] pros = Process.GetProcesses(".");
            foreach (Process p in pros)
            {
                bool a = p.ProcessName.Contains(processName);
                bool b = p.ProcessName.Equals(processName);
                if (a||b)
                {
                    p.Kill();
                }
            }
        }


    }
}
