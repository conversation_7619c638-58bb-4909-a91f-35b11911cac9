using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CCPanel.utils
{
    internal class GConfig
    {

        public static string[] serviceNameArray = { "mysql", "nginx", "redis", "emqx" };

        public const string SERVICE_NAME_MYSQL = "mysql";
        public const string SERVICE_NAME_NGINX = "nginx";
        public const string SERVICE_NAME_REDIS = "redis";
        public const string SERVICE_NAME_EMQX = "emqx";

        public const string PATH_HOME_SOFTWARE = "/system/software/";

        public const string PATH_HOME_JAVA_ENV = "/system/software/java/jdk1.8.0_131/jdk";
        public const string PATH_HOME_JAVA = "/system/software/java/jdk1.8.0_131/jdk/";

        public const string PATH_HOME_MYSQL = "/system/software/mysql/mysql-8.0.32-winx64/";
        public const string PATH_HOME_MYSQL_TOOLS = "/system/software/mysql/HeidiSQL11.0/";
        public const string PATH_HOME_MYSQL_TOOLS2 = "/system/software/mysql/Navicat_16.0.6.0/";
        public const string PATH_HOME_REDIS = "/system/software/redis/";
        public const string PATH_HOME_NGINX = "/system/software/nginx/";
        public const string PATH_HOME_NGINX_WEB = "/system/software/nginx/html/";
        public const string PATH_HOME_NGINX_CFG = "/system/software/nginx/conf/";
        public const string PATH_HOME_EMQX = "/system/software/emqx/bin/";

        public const string FILE_NAME_MYSQL = "mysql-install-win.bat";
        public const string File_NAME_MYSQL_UNINSTALL = "mysql-uninstall-win.bat";
        public const string FILE_NAME_SOFT_INIT = "EnvInit.bat";
        public const string FILE_NAME_REDIS = "redis-server.exe";
        public const string FILE_NAME_NGINX = "nginx-service.exe";
        public const string FILE_NAME_EMQX = "emqx.cmd";
        public const string FILE_NAME_MYSQL_TOOLS = "heidisql.exe";
        public const string FILE_NAME_MYSQL_TOOLS2 = "navicat.exe";

        public const string FILE_NAME_RUNTIME_VC2013_X64 = "vcredist_x64.exe";

        public const string PATH_CONFIG_RUNTIME = "/system/runtime/vc++2013/x64/";
        public const string PATH_INDOOR_SYSTEM_SERVER_HOME = "/system/server/";
        public const string PATH_INDOOR_SYSTEM_SERVER_LOG = "/system/server/sf-system-log";
        public const string INDOOR_SYSTEM_SERVER_NAME = "sftx-server.exe";


        public static String MSG_SERVICE_START = "服务已启动√";
        public static String MSG_SERVICE_STOP = "服务已关闭×";
        public static String MSG_SERVICE_NOT_EXIST = "服务不存在";


    }
}
