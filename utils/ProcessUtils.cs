using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.ServiceProcess;
using System.Diagnostics;
using System.Windows.Forms;

namespace CCPanel.shell
{
    internal class ProcessUtils
    {
        public static void KillProcess(string processName)
        {
            Process[] pros = Process.GetProcesses(".");
            foreach (Process p in pros)
            {
                bool a = p.ProcessName.Contains(processName);
                bool b = p.ProcessName.Equals(processName);
                if (a||b)
                {
                    p.Kill();
                }
            }
        }


    }
}
